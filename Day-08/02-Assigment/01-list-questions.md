# Basic-Level List Questions

**Q1: What is a list in Python, and how is it used in DevOps?**

**Q2: How do you create a list in Python, and can you provide an example related to DevOps?**

**Q3: What is the difference between a list and a tuple in Python, and when would you choose one over the other in a DevOps context?**

**Q4: How can you access elements in a list, and provide a DevOps-related example?**

**Q5: How do you add an element to the end of a list in Python? Provide a DevOps example.**

**Q6: How can you remove an element from a list in Python, and can you provide a DevOps use case?**

