# 待办事项管理器 v2.0

一个功能丰富的命令行待办事项管理器，支持数据持久化、任务优先级、截止日期等高级功能。

## 🚀 主要特性

### ✨ 核心功能
- **数据持久化**: 任务数据自动保存到JSON文件，程序重启后数据不丢失
- **任务优先级**: 支持低、中、高、紧急四个优先级别
- **任务状态**: 支持待办、进行中、已完成、已取消四种状态
- **截止日期**: 可为任务设置截止日期
- **任务搜索**: 支持关键词搜索任务
- **数据过滤**: 按状态或优先级过滤显示任务
- **任务统计**: 显示任务完成情况统计

### 🛠️ 技术特性
- **面向对象设计**: 使用类和枚举提高代码组织性
- **类型提示**: 完整的类型注解提高代码可读性
- **日志记录**: 记录所有操作日志便于调试
- **错误处理**: 完善的输入验证和异常处理
- **单元测试**: 全面的测试覆盖确保代码质量

## 📦 安装和运行

### 环境要求
- Python 3.7+
- 无需额外依赖包

### 运行程序
```bash
# 运行主程序
python optimized_todo.py

# 运行测试
python test_todo.py

# 运行原始版本对比
python daiban.py
```

## 🎯 使用指南

### 主菜单选项
```
🎯 待办事项管理器 v2.0
========================================
1️⃣  查看所有任务
2️⃣  添加新任务
3️⃣  标记任务完成
4️⃣  删除任务
5️⃣  编辑任务
6️⃣  搜索任务
7️⃣  任务统计
8️⃣  过滤显示
0️⃣  退出程序
```

### 任务优先级
- 🟢 **低优先级**: 不紧急的任务
- 🟡 **中优先级**: 普通任务（默认）
- 🟠 **高优先级**: 重要任务
- 🔴 **紧急**: 需要立即处理的任务

### 任务状态
- ⭕ **待办**: 尚未开始的任务
- 🔄 **进行中**: 正在处理的任务
- ✅ **已完成**: 已经完成的任务
- ❌ **已取消**: 取消的任务

## 📊 功能详解

### 1. 添加任务
- 输入任务内容
- 选择优先级（1-4）
- 可选设置截止日期（YYYY-MM-DD格式）

### 2. 查看任务
- 按优先级和创建时间排序显示
- 显示任务状态、优先级、内容和截止日期
- 支持按状态和优先级过滤

### 3. 搜索功能
- 支持关键词搜索任务内容
- 不区分大小写
- 显示匹配的任务数量

### 4. 统计信息
- 总任务数
- 各状态任务数量
- 高优先级任务数量
- 任务完成率

## 🗂️ 文件结构

```
Day-14/
├── optimized_todo.py    # 主程序文件
├── daiban.py           # 原始版本
├── test_todo.py        # 单元测试
├── config.py           # 配置文件
├── README.md           # 说明文档
├── todos.json          # 任务数据文件（自动生成）
└── todo_manager.log    # 日志文件（自动生成）
```

## 🔧 配置选项

在 `config.py` 中可以自定义：
- 数据文件路径
- 日志配置
- 显示设置
- 颜色配置

## 🧪 测试

运行单元测试：
```bash
python test_todo.py
```

测试覆盖：
- Task类的所有方法
- TodoManager类的核心功能
- 数据持久化
- 错误处理
- 边界条件

## 📈 优化对比

### 原版 vs 优化版

| 功能 | 原版 | 优化版 |
|------|------|--------|
| 数据持久化 | ❌ | ✅ JSON文件存储 |
| 任务优先级 | ❌ | ✅ 4个级别 |
| 任务状态 | 简单布尔值 | ✅ 4种状态 |
| 截止日期 | ❌ | ✅ 支持 |
| 搜索功能 | ❌ | ✅ 关键词搜索 |
| 过滤显示 | ❌ | ✅ 多维度过滤 |
| 任务编辑 | ❌ | ✅ 支持 |
| 统计信息 | ❌ | ✅ 详细统计 |
| 日志记录 | ❌ | ✅ 完整日志 |
| 错误处理 | 基础 | ✅ 完善处理 |
| 代码结构 | 函数式 | ✅ 面向对象 |
| 类型提示 | ❌ | ✅ 完整注解 |
| 单元测试 | ❌ | ✅ 全面测试 |

## 🚀 未来改进方向

1. **Web界面**: 开发Web版本界面
2. **数据库支持**: 支持SQLite/MySQL数据库
3. **多用户支持**: 支持多用户任务管理
4. **任务分类**: 添加任务分类/标签功能
5. **提醒功能**: 添加任务提醒和通知
6. **数据导出**: 支持导出为Excel/PDF
7. **API接口**: 提供REST API接口
8. **移动端**: 开发移动端应用

## 📝 更新日志

### v2.0 (当前版本)
- 完全重构为面向对象架构
- 添加数据持久化功能
- 支持任务优先级和状态
- 添加搜索和过滤功能
- 完善的错误处理和日志记录
- 全面的单元测试覆盖

### v1.0 (原始版本)
- 基础的待办事项管理
- 内存存储
- 简单的增删改查功能

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License