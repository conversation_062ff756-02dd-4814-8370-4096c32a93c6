import json
import os
from datetime import datetime, date


# ------------------------------ 数据持久化相关 ------------------------------
def load_todos():
    """从JSON文件加载待办事项（程序启动时调用）"""
    if os.path.exists("todos.json"):
        try:
            with open("todos.json", "r", encoding="utf-8") as f:
                # 加载数据并确保字段兼容（处理旧版本数据）
                todos = json.load(f)
                # 为旧任务补充缺失的字段（如priority、due_date、tags）
                for todo in todos:
                    if "priority" not in todo:
                        todo["priority"] = 2  # 默认中等优先级
                    if "due_date" not in todo:
                        todo["due_date"] = None
                    if "tags" not in todo:
                        todo["tags"] = []
                return todos
        except Exception as e:
            print(f"⚠️ 加载数据失败: {e}，将使用空列表")
    return []  # 首次运行或文件损坏时返回空列表


def save_todos(todos):
    """将待办事项保存到JSON文件（程序退出时调用）"""
    try:
        with open("todos.json", "w", encoding="utf-8") as f:
            # ensure_ascii=False：保留中文；indent=2：格式化输出
            json.dump(todos, f, ensure_ascii=False, indent=2, default=str)
        print("💾 数据已保存")
    except Exception as e:
        print(f"⚠️ 保存数据失败: {e}")


# ------------------------------ 菜单与主逻辑 ------------------------------
def show_menu():
    """显示操作菜单"""
    print("\n===== 待办事项管理器 =====")
    print("1. 查看所有任务（可筛选）")
    print("2. 添加新任务")
    print("3. 标记任务为完成")
    print("4. 编辑任务")
    print("5. 删除任务")
    print("6. 任务统计")
    print("7. 退出")
    return input("请输入操作序号（1-7）：").strip()


def main():
    todos = load_todos()  # 启动时加载数据
    if not todos:
        print("👋 欢迎使用待办事项管理器！当前无任务，请先添加~")

    while True:
        choice = show_menu()
        if choice == "1":
            show_todos(todos)
        elif choice == "2":
            add_todo(todos)
        elif choice == "3":
            mark_done(todos)
        elif choice == "4":
            edit_todo(todos)
        elif choice == "5":
            delete_todo(todos)
        elif choice == "6":
            show_stats(todos)
        elif choice == "7":
            save_todos(todos)  # 退出前保存数据
            print("👋 再见！")
            break
        else:
            print("❌ 无效选项，请输入1-7之间的数字！")


# ------------------------------ 任务核心功能 ------------------------------
def show_todos(todos):
    """查看所有任务（支持按标签/优先级筛选）"""
    if not todos:
        print("📭 当前没有待办事项，快去添加吧！")
        return

    # 筛选逻辑
    filter_tag = input("请输入筛选标签（直接回车显示全部）：").strip()
    filter_priority = input("请输入筛选优先级（1-高/2-中/3-低，回车显示全部）：").strip()

    # 应用筛选
    filtered = todos
    if filter_tag:
        filtered = [t for t in filtered if filter_tag in t["tags"]]
    if filter_priority in {"1", "2", "3"}:
        filtered = [t for t in filtered if t["priority"] == int(filter_priority)]

    if not filtered:
        print(f"🔍 未找到符合条件的任务")
        return

    # 按优先级+截止日期排序（高优先级>近截止日期）
    filtered_sorted = sorted(
        filtered,
        key=lambda x: (x["priority"], x["due_date"] or date.max)
    )

    # 显示任务
    print(f"\n===== 任务列表（共{len(filtered_sorted)}项） =====")
    for i, todo in enumerate(filtered_sorted, start=1):
        # 状态标记
        status = "✅" if todo["done"] else "🔲"
        # 优先级显示
        priority = ["🔴高", "🟡中", "🟢低"][todo["priority"] - 1]
        # 截止日期显示
        due_date = todo["due_date"]
        if due_date:
            try:
                due_date_obj = datetime.strptime(due_date, "%Y-%m-%d").date()
                today = date.today()
                if due_date_obj < today:
                    due_info = f"（已过期：{due_date}）"
                elif due_date_obj == today:
                    due_info = f"（今天截止：{due_date}）"
                else:
                    due_info = f"（剩余{str((due_date_obj - today).days)}天：{due_date}）"
            except:
                due_info = f"（日期格式错误：{due_date}）"
        else:
            due_info = "（无截止日期）"
        # 标签显示
        tags = f"【标签：{','.join(todo['tags'])}】" if todo["tags"] else ""

        print(f"{i}. {status} {priority} {todo['content']} {due_info} {tags}")


def add_todo(todos):
    """添加新任务（含优先级、截止日期、标签）"""
    # 任务内容
    content = input("请输入任务内容：").strip()
    if not content:
        print("❌ 任务内容不能为空！")
        return

    # 优先级
    while True:
        priority = input("请选择优先级（1-高/2-中/3-低，默认2）：").strip() or "2"
        if priority in {"1", "2", "3"}:
            priority = int(priority)
            break
        print("❌ 请输入1、2或3！")

    # 截止日期
    due_date = None
    while True:
        due_str = input("请输入截止日期（格式YYYY-MM-DD，回车无截止日期）：").strip()
        if not due_str:
            break
        try:
            # 验证日期格式
            datetime.strptime(due_str, "%Y-%m-%d")
            due_date = due_str
            break
        except ValueError:
            print("❌ 日期格式错误，请使用YYYY-MM-DD（例如2025-12-31）！")

    # 标签
    tags = input("请输入标签（用逗号分隔，如学习,重要，回车无标签）：").strip()
    tags = [t.strip() for t in tags.split(",") if t.strip()] if tags else []

    # 添加到列表
    todos.append({
        "content": content,
        "done": False,
        "priority": priority,
        "due_date": due_date,
        "tags": tags
    })
    print(f"✅ 已添加任务：{content}")


def mark_done(todos):
    """标记任务为完成"""
    if not todos:
        print("📭 没有任务可标记！")
        return

    show_todos(todos)
    try:
        index = int(input("请输入要标记完成的任务序号：")) - 1
    except ValueError:
        print("❌ 请输入有效的数字！")
        return

    if 0 <= index < len(todos):
        if todos[index]["done"]:
            print(f"ℹ️ 任务「{todos[index]['content']}」已完成，无需重复标记~")
        else:
            todos[index]["done"] = True
            print(f"✅ 已标记完成：{todos[index]['content']}")
    else:
        print("❌ 序号不存在！")


def edit_todo(todos):
    """编辑任务（内容/优先级/截止日期/标签）"""
    if not todos:
        print("📭 没有任务可编辑！")
        return

    show_todos(todos)
    try:
        index = int(input("请输入要编辑的任务序号：")) - 1
    except ValueError:
        print("❌ 请输入有效的数字！")
        return

    if not (0 <= index < len(todos)):
        print("❌ 序号不存在！")
        return

    todo = todos[index]
    print(f"\n当前任务：{todo['content']}")
    print("（直接回车保持当前值）")

    # 编辑内容
    new_content = input(f"新内容（当前：{todo['content']}）：").strip()
    if new_content:
        todo["content"] = new_content

    # 编辑优先级
    while True:
        new_prio = input(f"新优先级（1-高/2-中/3-低，当前：{todo['priority']}）：").strip()
        if not new_prio:
            break
        if new_prio in {"1", "2", "3"}:
            todo["priority"] = int(new_prio)
            break
        print("❌ 请输入1、2或3！")

    # 编辑截止日期
    while True:
        new_due = input(f"新截止日期（YYYY-MM-DD，当前：{todo['due_date'] or '无'}）：").strip()
        if not new_due:
            break
        try:
            datetime.strptime(new_due, "%Y-%m-%d")
            todo["due_date"] = new_due
            break
        except ValueError:
            print("❌ 日期格式错误，请使用YYYY-MM-DD！")

    # 编辑标签
    new_tags = input(f"新标签（逗号分隔，当前：{','.join(todo['tags']) or '无'}）：").strip()
    if new_tags:
        todo["tags"] = [t.strip() for t in new_tags.split(",") if t.strip()]
    elif new_tags == "":  # 输入空字符串则清空标签
        todo["tags"] = []

    print(f"✅ 任务已更新：{todo['content']}")


def delete_todo(todos):
    """删除任务（带确认）"""
    if not todos:
        print("📭 没有任务可删除！")
        return

    show_todos(todos)
    try:
        index = int(input("请输入要删除的任务序号：")) - 1
    except ValueError:
        print("❌ 请输入有效的数字！")
        return

    if 0 <= index < len(todos):
        todo = todos[index]
        confirm = input(f"确认删除「{todo['content']}」吗？（y/n）：").strip().lower()
        if confirm == "y":
            todos.pop(index)
            print(f"✅ 已删除任务：{todo['content']}")
        else:
            print("ℹ️ 已取消删除")
    else:
        print("❌ 序号不存在！")


def show_stats(todos):
    """显示任务统计信息"""
    if not todos:
        print("📭 当前没有任务，无法统计！")
        return

    total = len(todos)
    completed = sum(1 for t in todos if t["done"])
    incomplete = total - completed
    completion_rate = (completed / total) * 100 if total > 0 else 0

    print("\n===== 任务统计 =====")
    print(f"总任务数：{total}")
    print(f"已完成：{completed}（{completion_rate:.1f}%）")
    print(f"未完成：{incomplete}（{100 - completion_rate:.1f}%）")

    # 优先级分布
    prio_counts = {1: 0, 2: 0, 3: 0}
    for t in todos:
        prio_counts[t["priority"]] += 1
    print("\n优先级分布：")
    print(f"  高（1）：{prio_counts[1]}个（{prio_counts[1] / total * 100:.1f}%）")
    print(f"  中（2）：{prio_counts[2]}个（{prio_counts[2] / total * 100:.1f}%）")
    print(f"  低（3）：{prio_counts[3]}个（{prio_counts[3] / total * 100:.1f}%）")

    # 标签分布（取前5个最多的标签）
    tag_counts = {}
    for t in todos:
        for tag in t["tags"]:
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
    if tag_counts:
        print("\n标签分布（前5）：")
        top_tags = sorted(tag_counts.items(), key=lambda x: -x[1])[:5]
        for tag, count in top_tags:
            print(f"  {tag}：{count}个任务")


if __name__ == "__main__":
    main()