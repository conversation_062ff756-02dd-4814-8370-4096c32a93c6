#!/usr/bin/env python3
"""
优化版待办事项管理器
功能：数据持久化、任务优先级、截止日期、搜索过滤等
"""

import json
import os
from datetime import datetime, date
from typing import List, Dict, Optional, Any
from enum import Enum
import logging


class Priority(Enum):
    """任务优先级枚举"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4


class TaskStatus(Enum):
    """任务状态枚举"""
    TODO = "待办"
    IN_PROGRESS = "进行中"
    COMPLETED = "已完成"
    CANCELLED = "已取消"


class Task:
    """任务类"""
    
    def __init__(self, content: str, priority: Priority = Priority.MEDIUM, 
                 due_date: Optional[str] = None, task_id: Optional[int] = None):
        self.id = task_id or int(datetime.now().timestamp() * 1000)
        self.content = content
        self.priority = priority
        self.status = TaskStatus.TODO
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        self.due_date = due_date
        self.completed_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'content': self.content,
            'priority': self.priority.value,
            'status': self.status.value,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'due_date': self.due_date,
            'completed_at': self.completed_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        """从字典创建任务对象"""
        task = cls(
            content=data['content'],
            priority=Priority(data.get('priority', Priority.MEDIUM.value)),
            due_date=data.get('due_date'),
            task_id=data['id']
        )
        task.status = TaskStatus(data.get('status', TaskStatus.TODO.value))
        task.created_at = data.get('created_at', task.created_at)
        task.updated_at = data.get('updated_at', task.updated_at)
        task.completed_at = data.get('completed_at')
        return task
    
    def mark_completed(self):
        """标记任务为完成"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now().isoformat()
        self.updated_at = self.completed_at
    
    def update_content(self, new_content: str):
        """更新任务内容"""
        self.content = new_content
        self.updated_at = datetime.now().isoformat()
    
    def set_priority(self, priority: Priority):
        """设置任务优先级"""
        self.priority = priority
        self.updated_at = datetime.now().isoformat()
    
    def set_due_date(self, due_date: str):
        """设置截止日期"""
        self.due_date = due_date
        self.updated_at = datetime.now().isoformat()
    
    def __str__(self) -> str:
        """字符串表示"""
        priority_symbols = {
            Priority.LOW: "🟢",
            Priority.MEDIUM: "🟡", 
            Priority.HIGH: "🟠",
            Priority.URGENT: "🔴"
        }
        
        status_symbols = {
            TaskStatus.TODO: "⭕",
            TaskStatus.IN_PROGRESS: "🔄",
            TaskStatus.COMPLETED: "✅",
            TaskStatus.CANCELLED: "❌"
        }
        
        due_info = f" (截止: {self.due_date})" if self.due_date else ""
        return f"{status_symbols[self.status]} {priority_symbols[self.priority]} {self.content}{due_info}"


class TodoManager:
    """待办事项管理器"""
    
    def __init__(self, data_file: str = "todos.json"):
        self.data_file = data_file
        self.tasks: List[Task] = []
        self.setup_logging()
        self.load_tasks()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('todo_manager.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_tasks(self):
        """从文件加载任务"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.tasks = [Task.from_dict(task_data) for task_data in data]
                self.logger.info(f"成功加载 {len(self.tasks)} 个任务")
            except Exception as e:
                self.logger.error(f"加载任务失败: {e}")
                self.tasks = []
        else:
            self.logger.info("数据文件不存在，创建新的任务列表")
    
    def save_tasks(self):
        """保存任务到文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump([task.to_dict() for task in self.tasks], f, 
                         ensure_ascii=False, indent=2)
            self.logger.info(f"成功保存 {len(self.tasks)} 个任务")
        except Exception as e:
            self.logger.error(f"保存任务失败: {e}")
    
    def add_task(self, content: str, priority: Priority = Priority.MEDIUM, 
                 due_date: Optional[str] = None) -> bool:
        """添加新任务"""
        if not content.strip():
            print("❌ 任务内容不能为空！")
            return False
        
        task = Task(content.strip(), priority, due_date)
        self.tasks.append(task)
        self.save_tasks()
        print(f"✅ 已添加任务: {task.content}")
        self.logger.info(f"添加任务: {task.content}")
        return True
    
    def show_tasks(self, filter_status: Optional[TaskStatus] = None,
                   filter_priority: Optional[Priority] = None):
        """显示任务列表"""
        filtered_tasks = self.tasks
        
        if filter_status:
            filtered_tasks = [t for t in filtered_tasks if t.status == filter_status]
        
        if filter_priority:
            filtered_tasks = [t for t in filtered_tasks if t.priority == filter_priority]
        
        if not filtered_tasks:
            print("📝 当前没有符合条件的任务")
            return
        
        print(f"\n📋 任务列表 (共 {len(filtered_tasks)} 项)")
        print("=" * 50)
        
        # 按优先级和创建时间排序
        sorted_tasks = sorted(filtered_tasks, 
                            key=lambda x: (x.priority.value, x.created_at), 
                            reverse=True)
        
        for i, task in enumerate(sorted_tasks, 1):
            print(f"{i:2d}. {task}")
    
    def mark_completed(self, task_index: int) -> bool:
        """标记任务完成"""
        if not self._is_valid_index(task_index):
            return False
        
        task = self.tasks[task_index]
        if task.status == TaskStatus.COMPLETED:
            print("⚠️  任务已经完成了！")
            return False
        
        task.mark_completed()
        self.save_tasks()
        print(f"✅ 已完成任务: {task.content}")
        self.logger.info(f"完成任务: {task.content}")
        return True
    
    def delete_task(self, task_index: int) -> bool:
        """删除任务"""
        if not self._is_valid_index(task_index):
            return False
        
        task = self.tasks.pop(task_index)
        self.save_tasks()
        print(f"🗑️  已删除任务: {task.content}")
        self.logger.info(f"删除任务: {task.content}")
        return True
    
    def edit_task(self, task_index: int, new_content: str) -> bool:
        """编辑任务内容"""
        if not self._is_valid_index(task_index):
            return False
        
        if not new_content.strip():
            print("❌ 任务内容不能为空！")
            return False
        
        task = self.tasks[task_index]
        old_content = task.content
        task.update_content(new_content.strip())
        self.save_tasks()
        print(f"✏️  已更新任务: {old_content} -> {task.content}")
        self.logger.info(f"编辑任务: {old_content} -> {task.content}")
        return True
    
    def search_tasks(self, keyword: str) -> List[Task]:
        """搜索任务"""
        keyword = keyword.lower()
        results = [task for task in self.tasks 
                  if keyword in task.content.lower()]
        return results
    
    def get_statistics(self) -> Dict[str, int]:
        """获取任务统计信息"""
        stats = {
            'total': len(self.tasks),
            'completed': len([t for t in self.tasks if t.status == TaskStatus.COMPLETED]),
            'in_progress': len([t for t in self.tasks if t.status == TaskStatus.IN_PROGRESS]),
            'todo': len([t for t in self.tasks if t.status == TaskStatus.TODO]),
            'high_priority': len([t for t in self.tasks if t.priority in [Priority.HIGH, Priority.URGENT]])
        }
        return stats
    
    def _is_valid_index(self, index: int) -> bool:
        """验证索引是否有效"""
        if not self.tasks:
            print("📝 没有任务可操作！")
            return False
        
        if not (0 <= index < len(self.tasks)):
            print("❌ 序号不存在！")
            return False
        
        return True


class TodoApp:
    """待办事项应用程序"""
    
    def __init__(self):
        self.manager = TodoManager()
    
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*40)
        print("🎯 待办事项管理器 v2.0")
        print("="*40)
        print("1️⃣  查看所有任务")
        print("2️⃣  添加新任务")
        print("3️⃣  标记任务完成")
        print("4️⃣  删除任务")
        print("5️⃣  编辑任务")
        print("6️⃣  搜索任务")
        print("7️⃣  任务统计")
        print("8️⃣  过滤显示")
        print("0️⃣  退出程序")
        print("="*40)
    
    def get_user_choice(self) -> str:
        """获取用户选择"""
        return input("请选择操作 (0-8): ").strip()
    
    def get_task_index(self, prompt: str = "请输入任务序号: ") -> Optional[int]:
        """获取任务索引"""
        try:
            index = int(input(prompt)) - 1
            return index
        except ValueError:
            print("❌ 请输入有效的数字！")
            return None
    
    def add_task_interactive(self):
        """交互式添加任务"""
        content = input("📝 请输入任务内容: ").strip()
        if not content:
            print("❌ 任务内容不能为空！")
            return
        
        # 选择优先级
        print("\n优先级选择:")
        print("1. 🟢 低")
        print("2. 🟡 中 (默认)")
        print("3. 🟠 高")
        print("4. 🔴 紧急")
        
        priority_choice = input("选择优先级 (1-4, 默认2): ").strip()
        priority_map = {
            '1': Priority.LOW,
            '2': Priority.MEDIUM,
            '3': Priority.HIGH,
            '4': Priority.URGENT
        }
        priority = priority_map.get(priority_choice, Priority.MEDIUM)
        
        # 截止日期
        due_date = input("📅 截止日期 (YYYY-MM-DD, 可选): ").strip()
        if due_date and not self._validate_date(due_date):
            print("⚠️  日期格式无效，将忽略截止日期")
            due_date = None
        
        self.manager.add_task(content, priority, due_date)
    
    def search_interactive(self):
        """交互式搜索"""
        keyword = input("🔍 请输入搜索关键词: ").strip()
        if not keyword:
            print("❌ 搜索关键词不能为空！")
            return
        
        results = self.manager.search_tasks(keyword)
        if results:
            print(f"\n🔍 搜索结果 (找到 {len(results)} 项):")
            for i, task in enumerate(results, 1):
                print(f"{i:2d}. {task}")
        else:
            print("🔍 未找到匹配的任务")
    
    def show_statistics(self):
        """显示统计信息"""
        stats = self.manager.get_statistics()
        print("\n📊 任务统计:")
        print(f"总任务数: {stats['total']}")
        print(f"已完成: {stats['completed']}")
        print(f"进行中: {stats['in_progress']}")
        print(f"待办: {stats['todo']}")
        print(f"高优先级: {stats['high_priority']}")
        
        if stats['total'] > 0:
            completion_rate = (stats['completed'] / stats['total']) * 100
            print(f"完成率: {completion_rate:.1f}%")
    
    def filter_tasks_interactive(self):
        """交互式过滤任务"""
        print("\n过滤选项:")
        print("1. 按状态过滤")
        print("2. 按优先级过滤")
        print("3. 显示所有")
        
        choice = input("选择过滤方式 (1-3): ").strip()
        
        if choice == '1':
            print("\n状态选择:")
            print("1. 待办")
            print("2. 进行中")
            print("3. 已完成")
            status_choice = input("选择状态 (1-3): ").strip()
            status_map = {
                '1': TaskStatus.TODO,
                '2': TaskStatus.IN_PROGRESS,
                '3': TaskStatus.COMPLETED
            }
            status = status_map.get(status_choice)
            if status:
                self.manager.show_tasks(filter_status=status)
        
        elif choice == '2':
            print("\n优先级选择:")
            print("1. 🟢 低")
            print("2. 🟡 中")
            print("3. 🟠 高")
            print("4. 🔴 紧急")
            priority_choice = input("选择优先级 (1-4): ").strip()
            priority_map = {
                '1': Priority.LOW,
                '2': Priority.MEDIUM,
                '3': Priority.HIGH,
                '4': Priority.URGENT
            }
            priority = priority_map.get(priority_choice)
            if priority:
                self.manager.show_tasks(filter_priority=priority)
        
        elif choice == '3':
            self.manager.show_tasks()
    
    def _validate_date(self, date_str: str) -> bool:
        """验证日期格式"""
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    def run(self):
        """运行应用程序"""
        print("🎉 欢迎使用待办事项管理器！")
        
        while True:
            self.show_menu()
            choice = self.get_user_choice()
            
            if choice == '1':
                self.manager.show_tasks()
            
            elif choice == '2':
                self.add_task_interactive()
            
            elif choice == '3':
                self.manager.show_tasks()
                index = self.get_task_index("请输入要完成的任务序号: ")
                if index is not None:
                    self.manager.mark_completed(index)
            
            elif choice == '4':
                self.manager.show_tasks()
                index = self.get_task_index("请输入要删除的任务序号: ")
                if index is not None:
                    self.manager.delete_task(index)
            
            elif choice == '5':
                self.manager.show_tasks()
                index = self.get_task_index("请输入要编辑的任务序号: ")
                if index is not None:
                    new_content = input("请输入新的任务内容: ").strip()
                    if new_content:
                        self.manager.edit_task(index, new_content)
            
            elif choice == '6':
                self.search_interactive()
            
            elif choice == '7':
                self.show_statistics()
            
            elif choice == '8':
                self.filter_tasks_interactive()
            
            elif choice == '0':
                print("👋 感谢使用待办事项管理器，再见！")
                break
            
            else:
                print("❌ 无效选项，请重新选择")


if __name__ == "__main__":
    app = TodoApp()
    app.run()
