#!/usr/bin/env python3
"""
待办事项管理器测试文件
"""

import unittest
import tempfile
import os
import json
from datetime import datetime
from optimized_todo import Task, TodoManager, Priority, TaskStatus


class TestTask(unittest.TestCase):
    """测试Task类"""
    
    def setUp(self):
        """测试前准备"""
        self.task = Task("测试任务", Priority.HIGH, "2024-12-31")
    
    def test_task_creation(self):
        """测试任务创建"""
        self.assertEqual(self.task.content, "测试任务")
        self.assertEqual(self.task.priority, Priority.HIGH)
        self.assertEqual(self.task.status, TaskStatus.TODO)
        self.assertEqual(self.task.due_date, "2024-12-31")
        self.assertIsNotNone(self.task.id)
        self.assertIsNotNone(self.task.created_at)
    
    def test_task_to_dict(self):
        """测试任务转字典"""
        task_dict = self.task.to_dict()
        self.assertIn('id', task_dict)
        self.assertIn('content', task_dict)
        self.assertIn('priority', task_dict)
        self.assertIn('status', task_dict)
        self.assertEqual(task_dict['content'], "测试任务")
        self.assertEqual(task_dict['priority'], Priority.HIGH.value)
    
    def test_task_from_dict(self):
        """测试从字典创建任务"""
        task_dict = self.task.to_dict()
        new_task = Task.from_dict(task_dict)
        self.assertEqual(new_task.content, self.task.content)
        self.assertEqual(new_task.priority, self.task.priority)
        self.assertEqual(new_task.status, self.task.status)
        self.assertEqual(new_task.id, self.task.id)
    
    def test_mark_completed(self):
        """测试标记完成"""
        self.task.mark_completed()
        self.assertEqual(self.task.status, TaskStatus.COMPLETED)
        self.assertIsNotNone(self.task.completed_at)
    
    def test_update_content(self):
        """测试更新内容"""
        import time
        old_updated_at = self.task.updated_at
        time.sleep(0.001)  # 确保时间戳不同
        self.task.update_content("新的任务内容")
        self.assertEqual(self.task.content, "新的任务内容")
        self.assertNotEqual(self.task.updated_at, old_updated_at)
    
    def test_set_priority(self):
        """测试设置优先级"""
        self.task.set_priority(Priority.LOW)
        self.assertEqual(self.task.priority, Priority.LOW)
    
    def test_set_due_date(self):
        """测试设置截止日期"""
        self.task.set_due_date("2025-01-01")
        self.assertEqual(self.task.due_date, "2025-01-01")
    
    def test_task_str(self):
        """测试任务字符串表示"""
        task_str = str(self.task)
        self.assertIn("测试任务", task_str)
        self.assertIn("2024-12-31", task_str)


class TestTodoManager(unittest.TestCase):
    """测试TodoManager类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时文件用于测试
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json')
        self.temp_file.close()
        self.manager = TodoManager(self.temp_file.name)
    
    def tearDown(self):
        """测试后清理"""
        # 关闭日志处理器
        import logging
        logger = logging.getLogger('optimized_todo')
        for handler in logger.handlers[:]:
            handler.close()
            logger.removeHandler(handler)

        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

        # 清理日志文件
        log_file = "todo_manager.log"
        if os.path.exists(log_file):
            try:
                os.unlink(log_file)
            except PermissionError:
                pass  # 忽略权限错误
    
    def test_add_task(self):
        """测试添加任务"""
        result = self.manager.add_task("测试任务1", Priority.HIGH)
        self.assertTrue(result)
        self.assertEqual(len(self.manager.tasks), 1)
        self.assertEqual(self.manager.tasks[0].content, "测试任务1")
        self.assertEqual(self.manager.tasks[0].priority, Priority.HIGH)
    
    def test_add_empty_task(self):
        """测试添加空任务"""
        result = self.manager.add_task("")
        self.assertFalse(result)
        self.assertEqual(len(self.manager.tasks), 0)
    
    def test_mark_completed(self):
        """测试标记完成"""
        self.manager.add_task("测试任务")
        result = self.manager.mark_completed(0)
        self.assertTrue(result)
        self.assertEqual(self.manager.tasks[0].status, TaskStatus.COMPLETED)
    
    def test_mark_completed_invalid_index(self):
        """测试标记完成无效索引"""
        result = self.manager.mark_completed(0)
        self.assertFalse(result)
    
    def test_delete_task(self):
        """测试删除任务"""
        self.manager.add_task("测试任务")
        result = self.manager.delete_task(0)
        self.assertTrue(result)
        self.assertEqual(len(self.manager.tasks), 0)
    
    def test_delete_task_invalid_index(self):
        """测试删除任务无效索引"""
        result = self.manager.delete_task(0)
        self.assertFalse(result)
    
    def test_edit_task(self):
        """测试编辑任务"""
        self.manager.add_task("原始任务")
        result = self.manager.edit_task(0, "修改后的任务")
        self.assertTrue(result)
        self.assertEqual(self.manager.tasks[0].content, "修改后的任务")
    
    def test_edit_task_empty_content(self):
        """测试编辑任务为空内容"""
        self.manager.add_task("原始任务")
        result = self.manager.edit_task(0, "")
        self.assertFalse(result)
        self.assertEqual(self.manager.tasks[0].content, "原始任务")
    
    def test_search_tasks(self):
        """测试搜索任务"""
        self.manager.add_task("Python学习")
        self.manager.add_task("Java开发")
        self.manager.add_task("Python项目")
        
        results = self.manager.search_tasks("Python")
        self.assertEqual(len(results), 2)
        
        results = self.manager.search_tasks("Java")
        self.assertEqual(len(results), 1)
        
        results = self.manager.search_tasks("不存在")
        self.assertEqual(len(results), 0)
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        self.manager.add_task("任务1", Priority.HIGH)
        self.manager.add_task("任务2", Priority.LOW)
        self.manager.add_task("任务3", Priority.URGENT)
        self.manager.mark_completed(0)
        
        stats = self.manager.get_statistics()
        self.assertEqual(stats['total'], 3)
        self.assertEqual(stats['completed'], 1)
        self.assertEqual(stats['todo'], 2)
        self.assertEqual(stats['high_priority'], 2)  # HIGH + URGENT
    
    def test_save_and_load_tasks(self):
        """测试保存和加载任务"""
        # 添加一些任务
        self.manager.add_task("任务1", Priority.HIGH, "2024-12-31")
        self.manager.add_task("任务2", Priority.LOW)
        self.manager.mark_completed(0)
        
        # 创建新的管理器实例来测试加载
        new_manager = TodoManager(self.temp_file.name)
        
        # 验证任务被正确加载
        self.assertEqual(len(new_manager.tasks), 2)
        self.assertEqual(new_manager.tasks[0].content, "任务1")
        self.assertEqual(new_manager.tasks[0].priority, Priority.HIGH)
        self.assertEqual(new_manager.tasks[0].status, TaskStatus.COMPLETED)
        self.assertEqual(new_manager.tasks[0].due_date, "2024-12-31")
        self.assertEqual(new_manager.tasks[1].content, "任务2")
        self.assertEqual(new_manager.tasks[1].priority, Priority.LOW)
    
    def test_is_valid_index(self):
        """测试索引验证"""
        # 空列表
        self.assertFalse(self.manager._is_valid_index(0))
        
        # 添加任务后
        self.manager.add_task("测试任务")
        self.assertTrue(self.manager._is_valid_index(0))
        self.assertFalse(self.manager._is_valid_index(1))
        self.assertFalse(self.manager._is_valid_index(-1))


class TestPriorityAndStatus(unittest.TestCase):
    """测试优先级和状态枚举"""
    
    def test_priority_values(self):
        """测试优先级值"""
        self.assertEqual(Priority.LOW.value, 1)
        self.assertEqual(Priority.MEDIUM.value, 2)
        self.assertEqual(Priority.HIGH.value, 3)
        self.assertEqual(Priority.URGENT.value, 4)
    
    def test_status_values(self):
        """测试状态值"""
        self.assertEqual(TaskStatus.TODO.value, "待办")
        self.assertEqual(TaskStatus.IN_PROGRESS.value, "进行中")
        self.assertEqual(TaskStatus.COMPLETED.value, "已完成")
        self.assertEqual(TaskStatus.CANCELLED.value, "已取消")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
