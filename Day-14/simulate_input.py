#!/usr/bin/env python3
"""
模拟用户输入测试
"""

import sys
from io import StringIO
from optimized_todo import TodoApp

def test_with_simulated_input():
    """使用模拟输入测试"""
    print("=== 模拟输入测试 ===")
    
    # 创建应用
    app = TodoApp()
    
    # 检查初始状态
    print(f"初始任务数量: {len(app.manager.tasks)}")
    
    # 直接测试选项1的处理逻辑
    print("\n直接测试选项1的处理:")
    choice = '1'
    print(f"用户输入: {choice}")
    
    if choice == '1':
        print("🔍 调试: 用户选择了查看所有任务")
        print(f"🔍 调试: 当前任务数量 = {len(app.manager.tasks)}")
        app.manager.show_tasks()
    
    print("\n测试完成")

if __name__ == "__main__":
    test_with_simulated_input()
