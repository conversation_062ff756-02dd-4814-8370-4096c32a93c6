#!/usr/bin/env python3
"""
调试测试文件 - 测试查看任务功能
"""

from optimized_todo import <PERSON>doMana<PERSON>, Priority

def test_show_tasks():
    """测试显示任务功能"""
    print("=== 调试测试：查看任务功能 ===")
    
    # 创建管理器
    manager = TodoManager("debug_todos.json")
    
    # 清空现有任务
    manager.tasks = []
    
    print(f"1. 初始任务数量: {len(manager.tasks)}")
    
    # 添加一些测试任务
    manager.add_task("测试任务1", Priority.HIGH, "2024-12-31")
    manager.add_task("测试任务2", Priority.MEDIUM)
    manager.add_task("测试任务3", Priority.LOW, "2024-11-30")
    
    print(f"2. 添加后任务数量: {len(manager.tasks)}")
    
    # 显示所有任务
    print("\n3. 调用 show_tasks():")
    manager.show_tasks()
    
    # 标记一个任务完成
    print("\n4. 标记第一个任务完成:")
    manager.mark_completed(0)
    
    # 再次显示任务
    print("\n5. 完成任务后再次显示:")
    manager.show_tasks()
    
    # 清理测试文件
    import os
    if os.path.exists("debug_todos.json"):
        os.remove("debug_todos.json")
    if os.path.exists("todo_manager.log"):
        try:
            os.remove("todo_manager.log")
        except:
            pass

if __name__ == "__main__":
    test_show_tasks()
