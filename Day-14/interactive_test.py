#!/usr/bin/env python3
"""
交互式测试 - 模拟用户输入
"""

from optimized_todo import TodoApp, Priority
import sys
from io import StringIO

def test_interactive():
    """测试交互式功能"""
    print("=== 交互式测试 ===")
    
    # 创建应用
    app = TodoApp()
    
    # 先添加一些测试数据
    app.manager.add_task("交互测试任务1", Priority.HIGH)
    app.manager.add_task("交互测试任务2", Priority.MEDIUM)
    
    print(f"当前任务数量: {len(app.manager.tasks)}")
    
    # 直接调用显示任务
    print("\n直接调用 show_tasks():")
    app.manager.show_tasks()
    
    # 测试菜单显示
    print("\n测试菜单显示:")
    app.show_menu()
    
    # 清理
    import os
    if os.path.exists("todos.json"):
        os.remove("todos.json")
    if os.path.exists("todo_manager.log"):
        try:
            os.remove("todo_manager.log")
        except:
            pass

if __name__ == "__main__":
    test_interactive()
