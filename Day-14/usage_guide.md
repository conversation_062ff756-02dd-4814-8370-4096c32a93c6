# 待办事项管理器使用指南

## 🚀 快速开始

### 1. 运行程序
```bash
python Day-14/optimized_todo.py
```

### 2. 首次使用
程序首次运行时会显示：
```
🎉 欢迎使用待办事项管理器！
2025-07-29 10:58:02,296 - INFO - 数据文件不存在，创建新的任务列表
```

### 3. 主菜单
```
========================================
🎯 待办事项管理器 v2.0
========================================
1️⃣  查看所有任务
2️⃣  添加新任务
3️⃣  标记任务完成
4️⃣  删除任务
5️⃣  编辑任务
6️⃣  搜索任务
7️⃣  任务统计
8️⃣  过滤显示
0️⃣  退出程序
========================================
请选择操作 (0-8): 
```

## 🔧 功能详解

### 查看所有任务 (选项1)
- **输入**: `1`
- **功能**: 显示所有任务，按优先级排序
- **如果没有任务**: 显示 "📝 当前没有符合条件的任务"
- **有任务时**: 显示任务列表，格式如下：
  ```
  📋 任务列表 (共 4 项)
  ==================================================
   1. ⭕ 🔴 单元测试编写 (截止: 2024-10-15)
   2. ⭕ 🟠 学习Python基础语法 (截止: 2024-12-31)
   3. ⭕ 🟡 完成项目文档 (截止: 2024-11-30)
   4. ⭕ 🟢 代码重构
  ```

### 添加新任务 (选项2)
1. 输入任务内容
2. 选择优先级 (1-4)
3. 可选输入截止日期 (YYYY-MM-DD格式)

### 其他功能
- **选项3**: 标记任务完成
- **选项4**: 删除任务
- **选项5**: 编辑任务内容
- **选项6**: 搜索任务
- **选项7**: 查看统计信息
- **选项8**: 过滤显示任务
- **选项0**: 退出程序

## 🐛 常见问题排查

### 问题1: 输入1后没有显示任务
**可能原因**:
1. 当前没有任务数据
2. 数据文件为空或损坏

**解决方案**:
1. 先添加一些任务 (选择选项2)
2. 运行测试数据脚本：
   ```bash
   python Day-14/manual_test.py
   ```

### 问题2: 程序无法启动
**检查**:
1. Python版本是否为3.7+
2. 文件路径是否正确
3. 是否有写入权限

### 问题3: 数据丢失
**说明**: 数据保存在 `todos.json` 文件中，如果文件被删除，数据会丢失。

## 🧪 测试程序

### 添加测试数据
```bash
python Day-14/manual_test.py
```

### 运行单元测试
```bash
python Day-14/test_todo.py
```

### 模拟输入测试
```bash
python Day-14/simulate_input.py
```

## 📁 文件说明

- `optimized_todo.py` - 主程序
- `todos.json` - 数据文件 (自动生成)
- `todo_manager.log` - 日志文件 (自动生成)
- `test_todo.py` - 单元测试
- `config.py` - 配置文件

## 💡 使用技巧

1. **首次使用**: 建议先添加几个测试任务
2. **数据备份**: 定期备份 `todos.json` 文件
3. **查看日志**: 如有问题，查看 `todo_manager.log` 文件
4. **优先级**: 使用不同优先级来组织任务
5. **截止日期**: 为重要任务设置截止日期

## 🔄 完整使用流程示例

1. 启动程序: `python Day-14/optimized_todo.py`
2. 添加任务: 输入 `2` → 输入任务内容 → 选择优先级
3. 查看任务: 输入 `1`
4. 完成任务: 输入 `3` → 选择任务序号
5. 退出程序: 输入 `0`
