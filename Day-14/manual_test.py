#!/usr/bin/env python3
"""
手动测试程序 - 添加一些初始数据然后启动程序
"""

from optimized_todo import TodoApp, Priority
import os

def setup_test_data():
    """设置测试数据"""
    print("正在设置测试数据...")
    
    # 创建应用
    app = TodoApp()
    
    # 清空现有任务
    app.manager.tasks = []
    
    # 添加一些测试任务
    app.manager.add_task("学习Python基础语法", Priority.HIGH, "2024-12-31")
    app.manager.add_task("完成项目文档", Priority.MEDIUM, "2024-11-30")
    app.manager.add_task("代码重构", Priority.LOW)
    app.manager.add_task("单元测试编写", Priority.URGENT, "2024-10-15")
    
    print(f"已添加 {len(app.manager.tasks)} 个测试任务")
    print("现在可以运行主程序测试查看功能")
    print("运行命令: python Day-14/optimized_todo.py")
    print("然后输入 '1' 查看所有任务")

if __name__ == "__main__":
    setup_test_data()
