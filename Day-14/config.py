"""
配置文件
"""

import os
from pathlib import Path

# 数据文件配置
DATA_DIR = Path("data")
DATA_DIR.mkdir(exist_ok=True)

TODO_DATA_FILE = DATA_DIR / "todos.json"
LOG_FILE = DATA_DIR / "todo_manager.log"
BACKUP_DIR = DATA_DIR / "backups"
BACKUP_DIR.mkdir(exist_ok=True)

# 应用配置
APP_NAME = "待办事项管理器"
APP_VERSION = "2.0"

# 显示配置
MAX_DISPLAY_ITEMS = 50
DATE_FORMAT = "%Y-%m-%d"
DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"

# 自动备份配置
AUTO_BACKUP = True
BACKUP_INTERVAL_DAYS = 7
MAX_BACKUP_FILES = 10

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 颜色配置（用于终端显示）
COLORS = {
    'reset': '\033[0m',
    'red': '\033[91m',
    'green': '\033[92m',
    'yellow': '\033[93m',
    'blue': '\033[94m',
    'purple': '\033[95m',
    'cyan': '\033[96m',
    'white': '\033[97m',
    'bold': '\033[1m',
    'underline': '\033[4m'
}

# 优先级颜色映射
PRIORITY_COLORS = {
    1: COLORS['green'],    # LOW
    2: COLORS['yellow'],   # MEDIUM  
    3: COLORS['red'],      # HIGH
    4: COLORS['purple']    # URGENT
}
