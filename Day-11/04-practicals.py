# Server configurations dictionary
server_config = {
    'server1': {'ip': '***********', 'port': 8080, 'status': 'active'},
    'server2': {'ip': '***********', 'port': 8000, 'status': 'inactive'},
    'server3': {'ip': '***********', 'port': 9000, 'status': 'active'}
}

# Retrieving information
def get_server_status(server_name):
    return server_config.get(server_name, {}).get('status', 'Server not found')

# Example usage
server_name = 'server2'
status = get_server_status(server_name)
print(f"{server_name} status: {status}")
