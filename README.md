# Python Zero to Hero for DevOps Engineers

<img width="1141" alt="Screenshot 2023-10-12 at 9 57 40 PM" src="https://github.com/iam-veeramalla/python-for-devops/assets/43399466/d70f5fe2-0ba3-449d-b41f-413a38fc4584">

## Day 1: Introduction to Python, Installation, and Configuration
- Introduction to Python and its role in DevOps.
- Installing Python and setting up a development environment.
- Writing your first Python program.

## Day 2: Intro to Datatypes, Working with Strings and Numbers
- String data type in Python.
- String manipulation and formatting.
- Regular expressions for text processing.
- Numeric data types in Python (int, float).
 
## Day 3: Keywords and Variables
- Understanding variables in Python.
- Variable scope and lifetime.
- Variable naming conventions and best practices.
- Practice exercises and examples:
  - Example: Using variables to store and manipulate configuration data in a DevOps context.

## Day 4: Functions, Modules and Packages
- What are differences between function, modules and packages ?
- How to import a package ?
- What are Python workspaces ?

## Day 5: Environment Variables and Command Line Arguments
- Reading and writing environment variables in Python.
- Using the os and dotenv modules.
- Securing sensitive information in environment variables.
- Handling command line arguments in Python.
- Practice exercises and examples:
  - Example: Developing a Python script that accepts command line arguments to customize DevOps automation tasks.

## Day 6: Operators
- Introduction to operators in Python.
- Arithmetic, comparison, and logical operators.
- Bitwise and assignment operators.
- Practice exercises and examples:
  - Example: Using operators to perform calculations and comparisons in a DevOps script.

## Day 7: Conditional Handling using if, elif and else
- Conditional statements (if, elif, else).
- Practice exercises and examples:

## Day 8: Working with Lists (Part 1)
- Understanding lists and list data structure.
- List manipulation and common list operations.
- Practice exercises and examples:
  - Example: Writing a script to manage a list of user accounts in a DevOps environment.
  
## Day 9: Loops
- Loops in Python (for and while).
- Loop control statements (break, continue).
- Practice exercises and examples:
  - Example: Automating a log file analysis with a loop to find errors.

## Day 10: Working with Lists (Part 2)
- List comprehensions.
- Nested lists and advanced list operations.
- Practice exercises and examples:
  - Example: Print list of files in the list of folders provided

## Day 11: Working with Dictionaries and Sets (Project-1)
- Dictionaries and key-value pairs.
- Sets and set operations.
- Practice exercises and examples:
  - Example: Managing a dictionary of server configurations and optimizing retrieval.

## Day 12: Python Tasks for DevOps (Part 1) - File Operations (Project-2)
- Introduction to File Operations and Boto3.
- Automating File operations.
- Practice exercises and examples:
  - Example: Update a server resources in the server.conf file up on external notification.

## Day 13: Python Tasks for DevOps (Part 2) (Project-3)
- Using Fabric for remote task automation.
- AWS automation with Boto3.
- Managing EC2 instances, S3 buckets, and more.
- Practice exercises and examples:
  - Example: Creating a aws script for deploying applications to remote servers.

## Day 14: Github-JIRA intergration Project - (Project-4)
- Introduction to RESTful APIs.
- Making HTTP requests using Python.
- Parsing JSON responses and error handling.
- Practice exercises and examples:
  - Example: Write a Python API which listens on a github comment and creates a ticket in JIRA.

## Day 15: Github-JIRA intergration Project - (Project-4) - (Part-2)
- Introduction to Flask.
- Write your first API in python.
- How to handle API calls and deploy your API to a server.
- Practice exercises and examples:
  - Example: Write a Python API which listens on a github comment and creates a ticket in JIRA.

## Day 16: Python Interview Questions & Answers
- Beginner and intermediate Level

## Day 17: Python Interview Questions & Answers
- Advanced Level
