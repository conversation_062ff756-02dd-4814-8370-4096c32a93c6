
# Python Operators Assignment

In this assignment, you will explore various Python operators and their usage. Please complete the following tasks.

## Task 1: Arithmetic Operators

1. Create two variables `a` and `b` with numeric values.
2. Calculate the sum, difference, product, and quotient of `a` and `b`.
3. Print the results.

## Task 2: Comparison Operators

1. Compare the values of `a` and `b` using the following comparison operators: `<`, `>`, `<=`, `>=`, `==`, and `!=`.
2. Print the results of each comparison.

## Task 3: Logical Operators

1. Create two boolean variables, `x` and `y`.
2. Use logical operators (`and`, `or`, `not`) to perform various logical operations on `x` and `y`.
3. Print the results.

## Task 4: Assignment Operators

1. Create a variable `total` and initialize it to 10.
2. Use assignment operators (`+=`, `-=`, `*=`, `/=`) to update the value of `total`.
3. Print the final value of `total`.

## Task 5: Bitwise Operators (Optional)

1. If you are comfortable with bitwise operators, perform some bitwise operations on integer values and print the results. If not, you can skip this task.

## Task 6: Identity and Membership Operators

1. Create a list `my_list` containing a few elements.
2. Use identity operators (`is` and `is not`) to check if two variables are the same object.
3. Use membership operators (`in` and `not in`) to check if an element is present in `my_list`.
4. Print the results.


