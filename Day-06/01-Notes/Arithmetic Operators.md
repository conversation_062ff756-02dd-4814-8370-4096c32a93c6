# Arithmetic Operations in Python

## Introduction

Arithmetic operators in Python allow you to perform basic mathematical calculations on numeric values. These operators include addition, subtraction, multiplication, division, and more.

## List of Arithmetic Operators

1. **Addition (+):** Adds two numbers.
2. **Subtraction (-):** Subtracts the right operand from the left operand.
3. **Multiplication (*):** Multiplies two numbers.
4. **Division (/):** Divides the left operand by the right operand (results in a floating-point number).
5. **Floor Division (//):** Divides the left operand by the right operand and rounds down to the nearest whole number.
6. **Modulus (%):** Returns the remainder of the division of the left operand by the right operand.
7. **Exponentiation (**):** Raises the left operand to the power of the right operand.

## Examples

### Addition

```python
a = 5
b = 3
result = a + b
print(result)  # Output: 8
```

### Subtraction

```python
x = 10
y = 7
result = x - y
print(result)  # Output: 3
```